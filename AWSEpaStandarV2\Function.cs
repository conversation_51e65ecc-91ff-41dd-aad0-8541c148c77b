using Amazon.Lambda.Core;
using AWSEpaStandarV2.Helpers;
using AWSEpaStandarV2.Models;
using AWSEpaStandarV2.Service;
using Microsoft.Extensions.DependencyInjection;
using PureCloudPlatform.Client.V2.Client;
using PureCloudPlatform.Client.V2.Extensions;
using PureCloudPlatform.Client.V2.Model;
using System;
using Response = AWSEpaStandarV2.Models.Response;

// Assembly attribute to enable the Lambda function's JSON input to be converted into a .NET class.
[assembly: LambdaSerializer(typeof(Amazon.Lambda.Serialization.SystemTextJson.DefaultLambdaJsonSerializer))]

namespace AWSEpaStandarV2;

public class Function
{

    /// <summary>
    /// A simple function that takes a string and does a ToUpper
    /// </summary>
    /// <param name="input"></param>
    /// <param name="context"></param>
    /// <returns></returns>
    readonly ServiceProvider _serviceProvider;
    public Function()
    {
        Environment.SetEnvironmentVariable("HOME", "/mnt/");

        var retryConfig = new ApiClient.RetryConfiguration
        {
            MaxRetryTimeSec = 10,
            RetryMax = 5
        };

        Configuration.Default.ApiClient.RetryConfig = retryConfig;
        Configuration.Default.AutoReloadConfig = false;

        //Dependency Injection
        Console.WriteLine("Setting up the DI container");
        var serviceCollection = new ServiceCollection();
        Console.WriteLine("Adding a scoped service");
        serviceCollection.AddScoped<IConversationService, ConversationService>();
        serviceCollection.AddScoped<IPollService, PollService>();
        _serviceProvider = serviceCollection.BuildServiceProvider();
    }

    public Response FunctionHandler(Request input, ILambdaContext context)
    {
        Response resp = new Response();
        ResponseData respData = new ResponseData();

        if (input.region != null)
        {
            PureCloudRegionHosts region = Genesys.getPureCloudRegion(input.region);
            Configuration.Default.ApiClient.setBasePath(region);
        }
        else
        {
            PureCloudRegionHosts region = PureCloudRegionHosts.us_east_1;
            Configuration.Default.ApiClient.setBasePath(region);
        }

        using (var scope = _serviceProvider.CreateScope())
        {
            
            var pollService = scope.ServiceProvider.GetRequiredService<IPollService>();

            switch (input.function)
            {
                case 1:
                    Console.WriteLine($"[Función 1] INICIO - Procesando conversación: {input.respReq.connid}");
                    Console.WriteLine($"[Función 1] EncuestaId: {input.respReq.encusta_id}");

                    var accessTokenInfo = Configuration.Default.ApiClient.PostToken(input.respReq.clientId,
                    input.respReq.clientSecret);
                    Console.WriteLine("Access token=" + accessTokenInfo.AccessToken);
                    var conversationService = scope.ServiceProvider.GetRequiredService<IConversationService>();
                    Result result = conversationService.GetLastEpaData(input.respReq.connid);

                    // Logging para diagnóstico de función 1
                    Console.WriteLine($"[Función 1] Datos obtenidos para conversación {input.respReq.connid}:");
                    Console.WriteLine($"[Función 1] - UserName: '{result.userName}'");
                    Console.WriteLine($"[Función 1] - QueueName: '{result.queueName}'");
                    Console.WriteLine($"[Función 1] - UserId: '{result.userId}'");
                    Console.WriteLine($"[Función 1] - QueueId: '{result.queueId}'");
                    Console.WriteLine($"[Función 1] - MediaType: '{result.mediaType}'");
                    Console.WriteLine($"[Función 1] - EncuestaId: {input.respReq.encusta_id}");

                    resp.EpaId = pollService.XIXX_INSERTA_EPA_INICIAL_COLA(input.respReq.encusta_id, result.userName, input.respReq.connid, result.queueName, input.conectionData, result.mediaType);
                    Console.WriteLine($"[Función 1] EpaId generado: {resp.EpaId}");
                    Console.WriteLine($"[Función 1] COMPLETADO - Conversación: {input.respReq.connid}");
                    break;

                case 2:
                    Console.WriteLine($"[Función 2] INICIO - Insertando respuesta");
                    Console.WriteLine($"[Función 2] EpaId: {input.respReq2.epa_id}");
                    Console.WriteLine($"[Función 2] RespuestaId: {input.respReq2.respuesta_id}");

                    // Validar token si se proporciona
                    if (!string.IsNullOrEmpty(input.respReq?.clientId) && !string.IsNullOrEmpty(input.respReq?.clientSecret))
                    {
                        try
                        {
                            Console.WriteLine($"[Función 2] Validando token de acceso...");
                            var accessTokenInfo2 = Configuration.Default.ApiClient.PostToken(input.respReq.clientId, input.respReq.clientSecret);
                            Console.WriteLine($"[Función 2] ✅ Token válido. Access token obtenido exitosamente");
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[Función 2] ❌ ERROR: Token inválido o expirado");
                            Console.WriteLine($"[Función 2] ❌ Error de autenticación: {ex.Message}");
                            Console.WriteLine($"[Función 2] ❌ Verifique que clientId y clientSecret sean válidos y tengan los permisos correctos");
                            // No lanzar excepción, solo loggear el error y continuar
                        }
                    }
                    else
                    {
                        Console.WriteLine($"[Función 2] ⚠️ No se proporcionaron credenciales de token (clientId/clientSecret)");
                    }

                    resp.Validacion = pollService.XIXX_INSERTA_RESPUESTAS(input.respReq2.epa_id, input.respReq2.respuesta_id);

                    Console.WriteLine($"[Función 2] Resultado Validacion: {resp.Validacion}");
                    Console.WriteLine($"[Función 2] COMPLETADO");
                    break;

                case 3:
                    resp.Validacion = pollService.XIXX_INSERTA_RESPUESTAS_TEXTO(input.respReq3.epa_id, input.respReq3.pregunta_id, input.respReq3.texto, input.conectionData);
                    break;

                case 4:
                    try
                    {
                        var accessTokenInfo4 = Configuration.Default.ApiClient.PostToken(input.respReq.clientId,
                        input.respReq.clientSecret);
                        Console.WriteLine("Token de acceso obtenido exitosamente para función 4");
                        Console.WriteLine("Access token=" + accessTokenInfo4.AccessToken);
                        resp.EpaId = pollService.UpdateAgents(input.respList, input.conectionData);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"ERROR: Falló la autenticación para función 4: {ex.Message}");
                        Console.WriteLine("Verifique que clientId y clientSecret sean válidos y tengan los permisos correctos");
                        throw;
                    }
                    break;
                case 5:
                    var accessTokenInfo5 = Configuration.Default.ApiClient.PostToken(input.respReq.clientId,
                    input.respReq.clientSecret);
                    Console.WriteLine("Access token=" + accessTokenInfo5.AccessToken);
                    var conversationService5 = scope.ServiceProvider.GetRequiredService<IConversationService>();
                    Result result5 = conversationService5.GetLastEpaData(input.respReq.connid);

                    resp.queueName = result5.queueName;
                    resp.queueId = result5.queueId;
                    resp.userName = result5.userName;
                    resp.userId = result5.userId;
                    
                    break;

                case 0:
                    resp.Validacion = pollService.XIXX_INSERTA_EPA_INICIAL(input.epaEnvioEncuesta.conversationId, input.epaEnvioEncuesta.agentName, input.epaEnvioEncuesta.organizationId, input.epaEnvioEncuesta.queueName);
                    break;
                default:
                    return null;
            }

            return resp;
        }
    }
}
