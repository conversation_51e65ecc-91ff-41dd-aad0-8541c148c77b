﻿using PureCloudPlatform.Client.V2.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AWSEpaStandarV2.Helpers
{
    public class Genesys
    {
        public static PureCloudRegionHosts getPureCloudRegion(string region)
        {
            switch (region)
            {
                case "us_east_1":
                    return PureCloudRegionHosts.us_east_1;
                case "eu_west_1":
                    return PureCloudRegionHosts.eu_west_1;
                case "eu_central_1":
                    return PureCloudRegionHosts.eu_central_1;
                case "ap_northeast_1":
                    return PureCloudRegionHosts.ap_northeast_1;
                case "ap_southeast_1":
                    return PureCloudRegionHosts.ap_southeast_1;
                case "ca_central_1":
                    return PureCloudRegionHosts.ca_central_1;
                case "ap_northeast_2":
                    return PureCloudRegionHosts.ap_northeast_2;
                case "eu_west_2":
                    return PureCloudRegionHosts.eu_west_2;
                case "us_west_2":
                    return PureCloudRegionHosts.us_west_2;
                case "sa_east_1":
                    return PureCloudRegionHosts.sa_east_1;
                default:
                    return PureCloudRegionHosts.us_east_1;
            }

        }
    }
}
