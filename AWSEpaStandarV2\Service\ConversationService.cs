﻿using AWSEpaStandarV2.Models;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using PureCloudPlatform.Client.V2.Api;
using PureCloudPlatform.Client.V2.Model;
using System;
using System.Linq;

namespace AWSEpaStandarV2.Service
{
    public interface IConversationService
    {
        Result GetLastEpaData(string idConversation);
        string GetUserName(string idUser, string name, string idConversation);
    }

    public class ConversationService : IConversationService
    {
        public Result GetLastEpaData(string idConversation)
        {
            Result result = new Result();

            try
            {
                Console.WriteLine($"[GetLastEpaData] Iniciando para conversación: {idConversation}");
                ConversationsApi conversationsApi = new ConversationsApi();
                var conversations = conversationsApi.GetConversation(idConversation);
                Console.WriteLine($"[GetLastEpaData] Llamada API exitosa. ID Conversación: {conversations?.Id}");
                Console.WriteLine($"[GetLastEpaData] Participants count: {conversations.Participants?.Count ?? 0}");

                var participants = conversations.Participants?.Select(static p => new
                {
                    userId = p.UserId,
                    name = p.Name,
                    queueName = p.QueueName,
                    queueId = p.QueueId,
                    purpose = p.Purpose,
                    endTime = p.EndTime
                }).ToList();

                // Log each participant for debugging
                if (participants != null)
                {
                    Console.WriteLine($"[GetLastEpaData] === ANÁLISIS DE PARTICIPANTES ===");
                    for (int i = 0; i < participants.Count; i++)
                    {
                        var p = participants[i];
                        Console.WriteLine($"[GetLastEpaData] Participant {i}: UserId={p.userId}, Name={p.name}, Purpose={p.purpose}, QueueName={p.queueName}, EndTime={p.endTime}");
                    }

                    // Contar tipos de participantes
                    var agentCount = participants.Count(p => p.purpose == "agent");
                    var ivrCount = participants.Count(p => p.purpose == "ivr");
                    var customerCount = participants.Count(p => p.purpose == "customer");
                    var otherCount = participants.Count(p => p.purpose != "agent" && p.purpose != "ivr" && p.purpose != "customer");

                    Console.WriteLine($"[GetLastEpaData] Resumen: {agentCount} agentes, {ivrCount} IVRs, {customerCount} clientes, {otherCount} otros");
                    Console.WriteLine($"[GetLastEpaData] === FIN ANÁLISIS DE PARTICIPANTES ===");
                }

                // Serializo solo lo necesario
                string json = JsonConvert.SerializeObject(new { participants });

                // Deserializo dinámicamente
                JObject responseJson = JObject.Parse(json);

                string lastId = "";
                string name = "";
                string qName = "";
                string qId = "";
                string? mediaType = null;
                DateTime? aux = null;

                var participantsToken = responseJson["participants"];
                if (participantsToken != null && participantsToken.Type == JTokenType.Array)
                {
                    Console.WriteLine($"[GetLastEpaData] Analizando {participantsToken.Count()} participantes");

                    // Estrategia mejorada para encontrar el último agente real
                    var agents = new List<dynamic>();

                    Console.WriteLine($"[GetLastEpaData] === BÚSQUEDA DE AGENTES ===");

                    // Recopilar todos los agentes con información procesada
                    foreach (var participant in participantsToken)
                    {
                        string purpose = participant["purpose"]?.ToString();

                        if (purpose == "agent")
                        {
                            // Manejo seguro de fechas para evitar errores de conversión
                            DateTime? endTime = null;
                            DateTime? startTime = null;

                            try
                            {
                                if (participant["endTime"] != null && participant["endTime"].Type != JTokenType.Null)
                                {
                                    endTime = participant["endTime"].ToObject<DateTime>();
                                }
                            }
                            catch
                            {
                                endTime = null;
                            }

                            try
                            {
                                if (participant["startTime"] != null && participant["startTime"].Type != JTokenType.Null)
                                {
                                    startTime = participant["startTime"].ToObject<DateTime>();
                                }
                            }
                            catch
                            {
                                startTime = null;
                            }

                            agents.Add(new {
                                userId = participant["userId"]?.ToString(),
                                name = participant["name"]?.ToString(),
                                queueName = participant["queueName"]?.ToString(),
                                queueId = participant["queueId"]?.ToString(),
                                startTime = startTime,
                                endTime = endTime,
                                isActive = endTime == null
                            });

                            Console.WriteLine($"[GetLastEpaData] Agente encontrado: UserId={participant["userId"]}, Name={participant["name"]}, StartTime={startTime}, EndTime={endTime}, Activo={endTime == null}");
                        }
                        else
                        {
                            Console.WriteLine($"[GetLastEpaData] Participante no-agente ignorado: Purpose={purpose}, Name={participant["name"]}");
                        }
                    }

                    Console.WriteLine($"[GetLastEpaData] Total de agentes encontrados: {agents.Count}");

                    // Lógica mejorada para seleccionar el último agente real
                    if (agents.Any())
                    {
                        Console.WriteLine($"[GetLastEpaData] === SELECCIÓN DEL ÚLTIMO AGENTE ===");

                        // PASO 1: Buscar agentes activos (sin endTime)
                        var activeAgents = agents.Where(a => a.isActive).ToList();
                        if (activeAgents.Any())
                        {
                            Console.WriteLine($"[GetLastEpaData] Encontrados {activeAgents.Count} agentes activos");
                            // Tomar el agente activo con startTime más reciente
                            var selectedAgent = activeAgents.OrderByDescending(a => a.startTime ?? DateTime.MinValue).First();

                            lastId = selectedAgent.userId;
                            qName = selectedAgent.queueName;
                            qId = selectedAgent.queueId;
                            name = GetUserName(lastId, selectedAgent.name, idConversation);
                            Console.WriteLine($"[GetLastEpaData] ✓ AGENTE ACTIVO SELECCIONADO: UserId={lastId}, Name={name}, Queue={qName}");
                        }
                        else
                        {
                            // PASO 2: Si no hay agentes activos, buscar el agente con endTime más reciente
                            Console.WriteLine($"[GetLastEpaData] No hay agentes activos, buscando último agente que terminó");
                            var finishedAgents = agents.Where(a => !a.isActive && a.endTime != null).ToList();
                            if (finishedAgents.Any())
                            {
                                var selectedAgent = finishedAgents.OrderByDescending(a => a.endTime).First();

                                lastId = selectedAgent.userId;
                                qName = selectedAgent.queueName;
                                qId = selectedAgent.queueId;
                                name = GetUserName(lastId, selectedAgent.name, idConversation);
                                Console.WriteLine($"[GetLastEpaData] ✓ ÚLTIMO AGENTE TERMINADO SELECCIONADO: UserId={lastId}, Name={name}, Queue={qName}, EndTime={selectedAgent.endTime}");
                            }
                            else
                            {
                                // PASO 3: Si no hay agentes con endTime, tomar cualquier agente disponible
                                Console.WriteLine($"[GetLastEpaData] No hay agentes con endTime, seleccionando cualquier agente disponible");
                                var anyAgent = agents.OrderByDescending(a => a.startTime ?? DateTime.MinValue).First();

                                lastId = anyAgent.userId;
                                qName = anyAgent.queueName;
                                qId = anyAgent.queueId;
                                name = GetUserName(lastId, anyAgent.name, idConversation);
                                Console.WriteLine($"[GetLastEpaData] ✓ AGENTE DISPONIBLE SELECCIONADO: UserId={lastId}, Name={name}, Queue={qName}");
                            }
                        }
                        Console.WriteLine($"[GetLastEpaData] === FIN SELECCIÓN DEL ÚLTIMO AGENTE ===");
                    }
                    else
                    {
                        Console.WriteLine($"[GetLastEpaData] ⚠ ADVERTENCIA: No se encontraron agentes en la conversación. Esto puede indicar que:");
                        Console.WriteLine($"[GetLastEpaData]   - La conversación solo tuvo participantes IVR/sistema");
                        Console.WriteLine($"[GetLastEpaData]   - Los datos de participantes están incompletos");
                        Console.WriteLine($"[GetLastEpaData]   - La conversación es muy antigua y los datos han expirado");
                    }
                    if (string.IsNullOrEmpty(qName) && participantsToken.Count() > 0)
                    {
                        qName = participantsToken[0]["queueName"]?.ToString();
                    }
                }

                result.userId = lastId;
                result.userName = name;
                result.queueId = qId;
                result.queueName = qName;
                result.mediaType = mediaType;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"[GetLastEpaData] ERROR: Error calling GetConversation: {ex.Message}");

                // Manejo específico de diferentes tipos de errores
                if (ex.Message.Contains("403") || ex.Message.Contains("not.authorized"))
                {
                    Console.WriteLine($"[GetLastEpaData] ⚠ ERROR DE AUTORIZACIÓN (403): Las credenciales no tienen permisos para acceder a la conversación {idConversation}");
                    Console.WriteLine($"[GetLastEpaData] Posibles causas:");
                    Console.WriteLine($"[GetLastEpaData]   - Las credenciales (clientId/clientSecret) han expirado");
                    Console.WriteLine($"[GetLastEpaData]   - Las credenciales no tienen los permisos necesarios para conversations:conversation:view");
                    Console.WriteLine($"[GetLastEpaData]   - La conversación pertenece a una organización diferente");
                    Console.WriteLine($"[GetLastEpaData] Solución: Verificar y actualizar las credenciales en la configuración");
                }
                else if (ex.Message.Contains("404") || ex.Message.Contains("not.found"))
                {
                    Console.WriteLine($"[GetLastEpaData] Conversación {idConversation} no encontrada en Genesys (404). Probablemente es una conversación antigua que ha expirado.");
                    Console.WriteLine($"[GetLastEpaData] Las conversaciones en Genesys tienen un tiempo de retención limitado.");
                }
                else
                {
                    Console.WriteLine($"[GetLastEpaData] Error inesperado: {ex.StackTrace}");
                }
            }
            return result;
        }

        public string GetUserName(string idUser, string name, string idConversation)
        {
            try
            {
                UsersApi usersApi = new UsersApi();
                return usersApi.GetUser(idUser).Name;
            }
            catch
            {
                // Continuamos con el nombre por defecto
            }

            if (String.IsNullOrEmpty(name))
            {
                try
                {
                    RecordingApi recordingApi = new RecordingApi();
                    var convRecording = recordingApi.GetConversationRecordings(idConversation);
                    name = convRecording[0].Users[0].Name;
                }
                catch
                {
                    // Nada, seguimos con el flujo
                }
            }
            return name;
        }
    }
}
