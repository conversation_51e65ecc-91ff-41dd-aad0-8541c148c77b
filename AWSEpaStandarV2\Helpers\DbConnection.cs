﻿using Amazon.Lambda.Core;
using AWSEpaStandarV2.Models;
using Newtonsoft.Json;
using PureCloudPlatform.Client.V2.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Amazon;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;

namespace AWSEpaStandarV2.Helpers
{
    //public interface IDbConnection
    //{
    //    string GetStringConnection(ConectionData conectionData);
    //}

    //public class DbConnection
    //{
    //    public static string GetStringConnection(ConectionData conectionData)
    //    {
    //        string ConnectionString = string.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***", conectionData.server, conectionData.database, conectionData.username, conectionData.pwd);
    //        return ConnectionString;
    //    }
    //}

    public class DbConnection
    {
        
        //private SqlConnection connection;

        public DbConnection()
        {
            //string secretNameV2 = Environment.GetEnvironmentVariable("secretManager");

            //connectionSecret = GetSecret(secretNameV2);

            //string ConnectionString = String.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***", connectionSecret.server, connectionSecret.database, connectionSecret.username, connectionSecret.pwd);
            //connection = new SqlConnection(ConnectionString);

            //connection.Open();
        }

        public static string GetStringConnection()
        {    
            ConnectionSecret connectionSecret;

            string secretNameV2 = Environment.GetEnvironmentVariable("secretManager");

            connectionSecret = GetSecret(secretNameV2);

            string ConnectionString = String.Format("Data Source={0};Initial Catalog={1};User ID={2};Password=***", connectionSecret.server, connectionSecret.database, connectionSecret.username, connectionSecret.pwd);
            return ConnectionString;
        }

        //public void Close()
        //{
        //    connection.Close();
        //}
        public static ConnectionSecret GetSecret(string secretName)
        {
            //string secretName = "ReporteriaCrossnet/Sura/db-sura-rds";
            string region = "us-east-1";
            string secret = "";

            MemoryStream memoryStream = new MemoryStream();
            //dev
            IAmazonSecretsManager client = new AmazonSecretsManagerClient("********************", "o3BkcS+YSfl3fRs/0dikqlDywxMaFQ2VYLEti2GF");// RegionEndpoint.GetBySystemName(region)
            //prod
            //IAmazonSecretsManager client = new AmazonSecretsManagerClient(RegionEndpoint.GetBySystemName(region));
            GetSecretValueRequest request = new GetSecretValueRequest();
            request.SecretId = secretName;
            request.VersionStage = "AWSCURRENT"; // VersionStage defaults to AWSCURRENT if unspecified.

            GetSecretValueResponse response = null;

            try
            {
                response = client.GetSecretValueAsync(request).Result;
            }
            catch (Exception e)
            {
                LambdaLogger.Log("Excepcion GetSecret:" + e.Message);

                //throw;
            }

            // Decrypts secret using the associated KMS key.
            // Depending on whether the secret is a string or binary, one of these fields will be populated.
            if (response.SecretString != null)
            {
                secret = response.SecretString;
            }
            else
            {
                memoryStream = response.SecretBinary;
                StreamReader reader = new StreamReader(memoryStream);
                string decodedBinarySecret = System.Text.Encoding.UTF8.GetString(Convert.FromBase64String(reader.ReadToEnd()));
            }

            ConnectionSecret secretData = JsonConvert.DeserializeObject<ConnectionSecret>(secret);

            return secretData;
        }

    }
}
