﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AWSEpaStandarV2.Models
{
    internal class Models
    {
    }

    public class IdConversationList
    {
        public List<IdConversation> conversations { get; set; }
    }

    public class IdConversation
    {
        public string idConversation { get; set; }
    }
    public class Result
    {
        public string userId { get; set; }
        public string userName { get; set; }
        public string queueId { get; set; }
        public string queueName { get; set; }
        public string? mediaType { get; set; }

        public Result()
        {
            userId = "";
            userName = "";
        }
    }

    public class Response
    {
        public int Validacion { get; set; }
        public int EpaId { get; set; }
        public string userId { get; set; }
        public string userName { get; set; }
        public string queueId { get; set; }
        public string queueName { get; set; }
    }

    public class ResponseData
    {
        public string userId { get; set; }
        public string userName { get; set; }
        public string queueId { get; set; }
        public string queueName { get; set; }
    }

    public class Request
    {
        public int function { get; set; }
        public string region { get; set; }
        public EpaEnvioEncuesta epaEnvioEncuesta { get; set; }
        public IdConversationList? respList { get; set; }
        public EpaIniRequest respReq { get; set; }
        public RespuestaRequest respReq2 { get; set; }
        public RespuestaTextRequest respReq3 { get; set; }
        public ConectionData conectionData { get; set; }
    }

    public class EpaEnvioEncuesta
    {
        public string conversationId { get; set; }
        public string agentName { get; set; }
        public string queueName { get; set; }
        public string organizationId { get; set; }

    }

    public class ConectionData
    {
        public string server { get; set; }
        public string database { get; set; }
        public string username { get; set; }
        public string pwd { get; set; }
    }

    public class EpaIniRequest
    {
        public int encusta_id { get; set; }
        public string connid { get; set; }
        public string clientId { get; set; }
        public string clientSecret { get; set; }

    }

    public class RespuestaRequest
    {
        public int epa_id { get; set; }
        public int respuesta_id { get; set; }
    }

    public class RespuestaTextRequest
    {
        public int epa_id { get; set; }
        public int pregunta_id { get; set; }
        public string texto { get; set; }

    }

    public class Conversation
    {
        public string id { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string address { get; set; }
        public List<Participant> participants { get; set; }
        public string recordingState { get; set; }
        public List<Division> divisions { get; set; }
        public string selfUri { get; set; }
    }

    public class Participant
    {
        public string id { get; set; }
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string name { get; set; }
        public string userUri { get; set; }
        public string userId { get; set; }
        public string queueName { get; set; }
        public string queueId { get; set; }
        public string purpose { get; set; }
        public string participantType { get; set; }
        public string address { get; set; }
        public string ani { get; set; }
        public string dnis { get; set; }
        public bool wrapupRequired { get; set; }
        public Attributes attributes { get; set; }
        public List<Call> calls { get; set; }
        public object[] callbacks { get; set; }
        public object[] chats { get; set; }
        public object[] cobrowsesessions { get; set; }
        public object[] emails { get; set; }
        public object[] messages { get; set; }
        public object[] screenshares { get; set; }
        public object[] socialExpressions { get; set; }
        public object[] videos { get; set; }
        public string aniName { get; set; }
    }

    public class Attributes
    {
    }

    public class Call
    {
        public string state { get; set; }
        public string id { get; set; }
        public string direction { get; set; }
        public bool recording { get; set; }
        public string recordingState { get; set; }
        public bool muted { get; set; }
        public bool confined { get; set; }
        public bool held { get; set; }
        public List<Segment> segments { get; set; }
        public string disconnectType { get; set; }
        public DateTime disconnectedTime { get; set; }
        public List<object> disconnectReasons { get; set; }
        public string provider { get; set; }
        public Self self { get; set; }
        public Other other { get; set; }
        public bool afterCallWorkRequired { get; set; }
        public string peerId { get; set; }
    }

    public class Self
    {
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
        public string name { get; set; }
    }

    public class Other
    {
        public string name { get; set; }
        public string nameRaw { get; set; }
        public string addressNormalized { get; set; }
        public string addressRaw { get; set; }
        public string addressDisplayable { get; set; }
    }

    public class Segment
    {
        public DateTime startTime { get; set; }
        public DateTime endTime { get; set; }
        public string type { get; set; }
        public string howEnded { get; set; }
        public string disconnectType { get; set; }
    }

    public class Division
    {
        public Division1 division { get; set; }
        public List<Entity> entities { get; set; }
    }

    public class Division1
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Entity
    {
        public string id { get; set; }
        public string selfUri { get; set; }
    }

    public class Error
    {
        public int status { get; set; }
        public string code { get; set; }
        public string message { get; set; }
    }
}
