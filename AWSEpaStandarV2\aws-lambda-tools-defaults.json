{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "default", "region": "us-east-1", "configuration": "Release", "function-architecture": "x86_64", "function-runtime": "dotnet6", "function-memory-size": 512, "function-timeout": 900, "function-handler": "AWSEpaStandarV2::AWSEpaStandarV2.Function::FunctionHandler", "framework": "net6.0", "function-name": "EpaStandard2", "function-description": "epa version 2.", "package-type": "Zip", "function-role": "arn:aws:iam::941689365908:role/lambda_exec_EPA_Standard2.0", "function-subnets": "subnet-031fd6db5b26b4791,subnet-04901b025157afbf4", "function-security-groups": "sg-0570aad4e8a6b488f", "tracing-mode": "PassThrough", "environment-variables": "\"secretManager\"=\"EPACrossnet/PRESTADORES/CN_ENCUESTA\"", "image-tag": ""}