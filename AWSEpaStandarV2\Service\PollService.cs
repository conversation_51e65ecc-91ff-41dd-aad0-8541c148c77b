using AWSEpaStandarV2.Helpers;
using AWSEpaStandarV2.Models;
using Microsoft.Extensions.DependencyInjection;
using PureCloudPlatform.Client.V2.Model;
using System;
using System.Collections.Generic;
using System.Data.SqlClient;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace AWSEpaStandarV2.Service
{
    public interface IPollService
    {
        int XIXX_INSERTA_EPA_INICIAL_COLA(int ENCUESTA_ID, string AGENTE, string CONNID, string QUEUE, ConectionData conectionData, string? MEDIATYPE = null);
        int XIXX_INSERTA_RESPUESTAS(int ePA_ID, int rESPUESTAID, ConectionData? conectionData = null);
        int XIXX_INSERTA_RESPUESTAS_TEXTO(int ePA_ID, int pREGUNTAID, string tEXTO, ConectionData conectionData);
        int UpdateAgents(IdConversationList idConversationList, ConectionData conectionData);
        int UpdateAgent(string idConversation, ConectionData conectionData);
        int XIXX_INSERTA_EPA_INICIAL(string conversationId, string agentName, string organizationId, string queueName);
    }


    public class PollService : IPollService
    {
        readonly ServiceProvider _serviceProvider;

        public PollService()
        {
            var serviceCollection = new ServiceCollection();
            serviceCollection.AddScoped<IConversationService, ConversationService>();
            _serviceProvider = serviceCollection.BuildServiceProvider();
        }
        public int UpdateAgents(IdConversationList idConversationList, ConectionData conectionData)
        {
            int result = 0;
            int successCount = 0;

            if(idConversationList != null)
            {
                foreach(var conversation in idConversationList.conversations)
                {
                    try
                    {
                        int aux = UpdateAgent(conversation.idConversation, conectionData);
                        if (aux > 0)
                        {
                            successCount++;
                            Console.WriteLine($"✓ UpdateAgent para conversación {conversation.idConversation} EXITOSO: {aux}");
                        }
                        else if (aux == -1)
                        {
                            Console.WriteLine($"⚠ UpdateAgent para conversación {conversation.idConversation} OMITIDO: Conversación expirada/no encontrada");
                        }
                        else
                        {
                            Console.WriteLine($"✗ UpdateAgent para conversación {conversation.idConversation} FALLÓ: {aux}");
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"✗ ERROR actualizando agente para conversación {conversation.idConversation}: {ex.Message}");
                    }
                }
                result = successCount;
            }
            return result;
        }

        public int UpdateAgent(string idConversation, ConectionData conectionData)
        {
            int result;
            Result conversation = new Result();

            using (var scope = _serviceProvider.CreateScope())
            {
                var conversationService = scope.ServiceProvider.GetRequiredService<IConversationService>();
                Console.WriteLine($"Obteniendo datos de conversación para: {idConversation}");
                conversation = conversationService.GetLastEpaData(idConversation);
                Console.WriteLine($"Datos obtenidos - UserId: '{conversation.userId}', UserName: '{conversation.userName}', QueueId: '{conversation.queueId}', QueueName: '{conversation.queueName}'");

                // Validar que tenemos los datos mínimos requeridos
                if (string.IsNullOrEmpty(conversation.userName))
                {
                    Console.WriteLine($"ADVERTENCIA: No se encontró nombre de agente para conversación {idConversation}");
                }
                if (string.IsNullOrEmpty(conversation.queueName))
                {
                    Console.WriteLine($"ADVERTENCIA: No se encontró nombre de cola para conversación {idConversation}");
                }

                // Si no tenemos ningún dato, esta conversación probablemente ya no existe
                if (string.IsNullOrEmpty(conversation.userName) &&
                    string.IsNullOrEmpty(conversation.queueName) &&
                    string.IsNullOrEmpty(conversation.userId))
                {
                    Console.WriteLine($"ERROR: La conversación {idConversation} parece estar expirada o inválida. Omitiendo actualización.");
                    return -1; // Retorna -1 para indicar conversación no encontrada/expirada
                }
            }
            using (SqlConnection connection = new SqlConnection(DbConnection.GetStringConnection()))
            {
                //set stored procedure name
                string spName = @"[enc].[XIXX_UPDATE_AGENTE_EPA_INICIAL]";

                //define the SqlCommand object
                using (var Cmd = new SqlCommand(spName, connection))
                {
                    Cmd.CommandType = System.Data.CommandType.StoredProcedure;

                    try
                    {
                        // Open SQL Connection
                        connection.Open();

                        var encuestaId = new SqlParameter()
                        {
                            ParameterName = "@CONNID",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = idConversation
                        };

                        var agente = new SqlParameter()
                        {
                            ParameterName = "@AGENTE",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 50,
                            Value = string.IsNullOrEmpty(conversation.userName) ? DBNull.Value : conversation.userName
                        };

                        var queue = new SqlParameter()
                        {
                            ParameterName = "@QUEUE",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 150,
                            Value = string.IsNullOrEmpty(conversation.queueName) ? DBNull.Value : conversation.queueName
                        };

                        var epaId = new SqlParameter()
                        {
                            ParameterName = "@EPA_ID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output,
                            SqlValue = 0
                        };


                        Cmd.Parameters.Add(encuestaId);
                        Cmd.Parameters.Add(agente);
                        Cmd.Parameters.Add(epaId);
                        Cmd.Parameters.Add(queue);

                        // Loop through the results and add to list

                        // Execute SQL Command
                        Console.WriteLine($"Ejecutando procedimiento almacenado: {spName} para conversación: {idConversation}");
                        Console.WriteLine($"Parámetros: CONNID={idConversation}, AGENTE={conversation.userName}, QUEUE={conversation.queueName}");

                        SqlDataReader rdr = Cmd.ExecuteReader();
                        rdr.Close(); // Cerrar el reader para acceder a parámetros de salida

                        // Obtener el valor del parámetro de salida
                        object outputValue = epaId.Value;
                        Console.WriteLine($"Valor parámetro de salida: {outputValue} (Tipo: {outputValue?.GetType()})");

                        result = -3; // Valor de error por defecto
                        if (outputValue != null && outputValue != DBNull.Value)
                        {
                            if (int.TryParse(outputValue.ToString(), out int parsedResult))
                            {
                                result = parsedResult;
                                Console.WriteLine($"Resultado parseado exitosamente: {result}");
                            }
                            else
                            {
                                Console.WriteLine($"Falló al parsear valor de salida: {outputValue}");
                            }
                        }
                        else
                        {
                            Console.WriteLine("Parámetro de salida es null o DBNull");
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error: " + ex);
                        Console.WriteLine("Mensaje de Error: " + ex.Message);
                        throw;
                    }
                }
            }
        }
        public int XIXX_INSERTA_EPA_INICIAL_COLA(int ENCUESTA_ID, string AGENTE, string CONNID, string QUEUE, ConectionData conectionData, string? MEDIATYPE = null)
        {
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] Iniciando inserción:");
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] - ENCUESTA_ID: {ENCUESTA_ID}");
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] - AGENTE: '{AGENTE}'");
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] - CONNID: '{CONNID}'");
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] - QUEUE: '{QUEUE}'");
            Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] - MEDIATYPE: '{MEDIATYPE}'");

            int result;
            using (SqlConnection connection = new SqlConnection(DbConnection.GetStringConnection()))
            {
                //set stored procedure name
                string spName = @"[enc].[XIXX_INSERTA_EPA_INICIAL_COLA]";

                //define the SqlCommand object
                using (var Cmd = new SqlCommand(spName, connection))
                {
                    Cmd.CommandType = System.Data.CommandType.StoredProcedure;

                    try
                    {
                        // Open SQL Connection
                        connection.Open();

                        var encuestaId = new SqlParameter()
                        {
                            ParameterName = "@ENCUESTA_ID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = ENCUESTA_ID
                        };

                        var agente = new SqlParameter()
                        {
                            ParameterName = "@AGENTE",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 50,
                            Value = AGENTE
                        };

                        var connid = new SqlParameter()
                        {
                            ParameterName = "@CONNID",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Size = 150,
                            Direction = System.Data.ParameterDirection.Input,
                            Value = CONNID
                        };

                        var queue = new SqlParameter()
                        {
                            ParameterName = "@QUEUE",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Size = 150,
                            Direction = System.Data.ParameterDirection.Input,
                            Value = QUEUE
                        };

                        //var mediaType = new SqlParameter()
                        //{
                        //    ParameterName = "@MEDIATYPE",
                        //    SqlDbType = System.Data.SqlDbType.VarChar,
                        //    Size = 150,
                        //    Direction = System.Data.ParameterDirection.Input,
                        //    Value = MEDIATYPE
                        //};

                        var epaId = new SqlParameter()
                        {
                            ParameterName = "@EPA_ID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output,
                            SqlValue = 0
                        };


                        Cmd.Parameters.Add(encuestaId);
                        Cmd.Parameters.Add(agente);
                        Cmd.Parameters.Add(connid);
                        Cmd.Parameters.Add(queue);
                        Cmd.Parameters.Add(epaId);
                        //Cmd.Parameters.Add(mediaType);

                        // Loop through the results and add to list

                        // Execute SQL Command

                        SqlDataReader rdr = Cmd.ExecuteReader();

                        string resultado = epaId.SqlValue.ToString();
                        result = -3;
                        if (resultado != null && resultado != "Null")
                        {
                            result = int.Parse(resultado);
                        }

                        Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] Resultado del stored procedure: {result}");
                        Console.WriteLine($"[XIXX_INSERTA_EPA_INICIAL_COLA] FINALIZADO para conversación: {CONNID}");
                        return result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error: " + ex);
                        Console.WriteLine("Error Message: " + ex.Message);
                        throw;
                    }
                }
            }
        }

        public int XIXX_INSERTA_RESPUESTAS(int ePA_ID, int rESPUESTAID, ConectionData? conectionData = null)
        {
            int result;

            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Usando conexión por defecto desde AWS Secrets Manager");

            using (var Conn = new SqlConnection(DbConnection.GetStringConnection()))
            {
                //set stored procedure name
                string spName = @"[enc].[XIXX_INSERTA_RESPUESTAS]";

                //define the SqlCommand object
                using (var Cmd = new SqlCommand(spName, Conn))
                {

                    Cmd.CommandType = System.Data.CommandType.StoredProcedure;

                    try
                    {
                        // Open SQL Connection
                        Conn.Open();

                        // Logging para diagnóstico
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Ejecutando procedimiento: {spName}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Parámetros: EPAID={ePA_ID}, RESPUESTAID={rESPUESTAID}");

                        // Verificación básica de conectividad
                        using (var testCmd = new SqlCommand("SELECT GETDATE() as CurrentTime", Conn))
                        {
                            var currentTime = testCmd.ExecuteScalar();
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Conexión DB exitosa. Hora actual: {currentTime}");
                        }

                        // VALIDACIONES ESPECÍFICAS PARA DIAGNÓSTICO
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === INICIANDO VALIDACIONES DE DIAGNÓSTICO ===");

                        // Primero, vamos a listar las tablas disponibles para encontrar los nombres correctos
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === LISTANDO TABLAS DISPONIBLES ===");
                        try
                        {
                            using (var listCmd = new SqlCommand(@"
                                SELECT DISTINCT
                                    SCHEMA_NAME(schema_id) as SchemaName,
                                    name as TableName
                                FROM sys.tables
                                WHERE name LIKE '%EPA%' OR name LIKE '%RESPUESTA%' OR name LIKE '%ENCUESTA%'
                                ORDER BY SchemaName, TableName", Conn))
                            {
                                using (var reader = listCmd.ExecuteReader())
                                {
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Tablas encontradas relacionadas con EPA/RESPUESTA/ENCUESTA:");
                                    while (reader.Read())
                                    {
                                        string schema = reader["SchemaName"].ToString();
                                        string table = reader["TableName"].ToString();
                                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] - [{schema}].[{table}]");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error listando tablas: {ex.Message}");
                        }

                        // Verificar estructura de las tablas principales
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === VERIFICANDO ESTRUCTURA DE TABLAS ===");

                        // Estructura de INTERACCION_ENCUESTA
                        try
                        {
                            using (var structCmd = new SqlCommand(@"
                                SELECT COLUMN_NAME, DATA_TYPE
                                FROM INFORMATION_SCHEMA.COLUMNS
                                WHERE TABLE_SCHEMA = 'enc' AND TABLE_NAME = 'INTERACCION_ENCUESTA'
                                ORDER BY ORDINAL_POSITION", Conn))
                            {
                                using (var reader = structCmd.ExecuteReader())
                                {
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Columnas en [enc].[INTERACCION_ENCUESTA]:");
                                    while (reader.Read())
                                    {
                                        string columnName = reader["COLUMN_NAME"].ToString();
                                        string dataType = reader["DATA_TYPE"].ToString();
                                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] - {columnName} ({dataType})");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error verificando estructura INTERACCION_ENCUESTA: {ex.Message}");
                        }

                        // Estructura de RESPUESTA
                        try
                        {
                            using (var structCmd = new SqlCommand(@"
                                SELECT COLUMN_NAME, DATA_TYPE
                                FROM INFORMATION_SCHEMA.COLUMNS
                                WHERE TABLE_SCHEMA = 'enc' AND TABLE_NAME = 'RESPUESTA'
                                ORDER BY ORDINAL_POSITION", Conn))
                            {
                                using (var reader = structCmd.ExecuteReader())
                                {
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Columnas en [enc].[RESPUESTA]:");
                                    while (reader.Read())
                                    {
                                        string columnName = reader["COLUMN_NAME"].ToString();
                                        string dataType = reader["DATA_TYPE"].ToString();
                                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] - {columnName} ({dataType})");
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error verificando estructura RESPUESTA: {ex.Message}");
                        }

                        // 1. Verificar si EPA_ID existe en INTERACCION_ENCUESTA
                        bool epaExists = false;
                        try
                        {
                            using (var checkCmd = new SqlCommand("SELECT COUNT(*) FROM [enc].[INTERACCION_ENCUESTA] WHERE EPA_ID = @EPAID", Conn))
                            {
                                checkCmd.Parameters.AddWithValue("@EPAID", ePA_ID);
                                int count = (int)checkCmd.ExecuteScalar();
                                epaExists = count > 0;
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ✓ EPA_ID {ePA_ID} existe en [enc].[INTERACCION_ENCUESTA]: {epaExists} (registros encontrados: {count})");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error verificando EPA_ID en [enc].[INTERACCION_ENCUESTA]: {ex.Message}");
                        }

                        // 2. Verificar si RESPUESTA_ID existe en RESPUESTA
                        bool respuestaExists = false;
                        try
                        {
                            using (var checkCmd = new SqlCommand("SELECT COUNT(*) FROM [enc].[RESPUESTA] WHERE RESPUESTA_ID = @RESPUESTAID", Conn))
                            {
                                checkCmd.Parameters.AddWithValue("@RESPUESTAID", rESPUESTAID);
                                int count = (int)checkCmd.ExecuteScalar();
                                respuestaExists = count > 0;
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ✓ RESPUESTA_ID {rESPUESTAID} existe en [enc].[RESPUESTA]: {respuestaExists} (registros encontrados: {count})");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error verificando RESPUESTA_ID en [enc].[RESPUESTA]: {ex.Message}");
                        }

                        // 3. Verificar si ya existe una respuesta para este EPA_ID
                        bool combinacionExists = false;
                        try
                        {
                            using (var checkCmd = new SqlCommand("SELECT COUNT(*) FROM [enc].[RESPUESTA] WHERE EPA_ID = @EPAID AND RESPUESTA_ID = @RESPUESTAID", Conn))
                            {
                                checkCmd.Parameters.AddWithValue("@EPAID", ePA_ID);
                                checkCmd.Parameters.AddWithValue("@RESPUESTAID", rESPUESTAID);
                                int count = (int)checkCmd.ExecuteScalar();
                                combinacionExists = count > 0;
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ✓ Combinación EPA_ID {ePA_ID} + RESPUESTA_ID {rESPUESTAID} ya existe en [enc].[RESPUESTA]: {combinacionExists} (registros encontrados: {count})");
                            }
                        }
                        catch (Exception ex)
                        {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ Error verificando combinación en [enc].[RESPUESTA]: {ex.Message}");
                        }

                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === RESUMEN DE VALIDACIONES ===");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] EPA_ID {ePA_ID} existe: {epaExists}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] RESPUESTA_ID {rESPUESTAID} existe: {respuestaExists}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Combinación ya existe: {combinacionExists}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === FIN VALIDACIONES ===");

                        var epaId = new SqlParameter()
                        {
                            ParameterName = "@EPAID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = ePA_ID
                        };

                        var respuestaId = new SqlParameter()
                        {
                            ParameterName = "@RESPUESTAID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = rESPUESTAID
                        };

                        var validacion = new SqlParameter()
                        {
                            ParameterName = "@VALIDACION",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };


                        Cmd.Parameters.Add(epaId);
                        Cmd.Parameters.Add(respuestaId);
                        Cmd.Parameters.Add(validacion);
                        // Loop through the results and add to list

                        // Configurar el manejador de mensajes antes de ejecutar
                        Conn.InfoMessage += (sender, e) => {
                            Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Info del servidor: {e.Message}");
                        };

                        // Execute SQL Command
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Ejecutando stored procedure...");

                        // Usar ExecuteNonQuery en lugar de ExecuteReader para procedimientos que no devuelven resultados
                        int rowsAffected = Cmd.ExecuteNonQuery();
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Filas afectadas: {rowsAffected}");

                        // Obtener el valor del parámetro de salida
                        object outputValue = validacion.Value;
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Valor parámetro de salida VALIDACION: {outputValue}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Tipo del parámetro: {outputValue?.GetType()}");

                        result = -3;
                        if (outputValue != null && outputValue != DBNull.Value)
                        {
                            result = Convert.ToInt32(outputValue);
                        }

                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] === RESULTADO FINAL ===");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Resultado del procedimiento: {result}");

                        // Interpretación del resultado
                        switch (result)
                        {
                            case 0:
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ FALLO: El procedimiento devolvió 0 (no se insertó)");
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] 🔍 DIAGNÓSTICO BASADO EN VALIDACIONES:");
                                if (!epaExists)
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ EPA_ID {ePA_ID} NO EXISTE en la tabla EPA");
                                if (!respuestaExists)
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ RESPUESTA_ID {rESPUESTAID} NO EXISTE en la tabla RESPUESTAS");
                                if (combinacionExists)
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❌ La combinación EPA_ID {ePA_ID} + RESPUESTA_ID {rESPUESTAID} YA EXISTE");
                                if (epaExists && respuestaExists && !combinacionExists)
                                    Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❓ Los datos existen pero el procedimiento falló por otra razón (validaciones internas del SP)");
                                break;
                            case 1:
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ✅ ÉXITO: Respuesta insertada correctamente");
                                break;
                            case -1:
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ⚠️ ADVERTENCIA: Posible duplicado o restricción");
                                break;
                            default:
                                Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] ❓ Resultado inesperado: {result}");
                                break;
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Error: {ex}");
                        Console.WriteLine($"[XIXX_INSERTA_RESPUESTAS] Error Message: {ex.Message}");
                        throw;
                    }
                }
            }
        }

        public int XIXX_INSERTA_RESPUESTAS_TEXTO(int ePA_ID, int pREGUNTAID, string tEXTO, ConectionData conectionData)
        {
            int result;

            using (var Conn = new SqlConnection(DbConnection.GetStringConnection()))
            {
                //set stored procedure name
                string spName = @"[enc].[XIXX_INSERTA_RESPUESTAS_TEXTO]";

                //define the SqlCommand object
                using (var Cmd = new SqlCommand(spName, Conn))
                {

                    Cmd.CommandType = System.Data.CommandType.StoredProcedure;

                    try
                    {
                        // Open SQL Connection
                        Conn.Open();

                        var epaId = new SqlParameter()
                        {
                            ParameterName = "@EPAID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = ePA_ID
                        };

                        var preguntaId = new SqlParameter()
                        {
                            ParameterName = "@PREGUNTAID",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = pREGUNTAID
                        };

                        var texto = new SqlParameter()
                        {
                            ParameterName = "@TEXTO",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Size = 1000,
                            Direction = System.Data.ParameterDirection.Input,
                            IsNullable = false,
                            Value = tEXTO
                        };

                        var validacion = new SqlParameter()
                        {
                            ParameterName = "@VALIDACION",
                            SqlDbType = System.Data.SqlDbType.Int,
                            Direction = System.Data.ParameterDirection.Output
                        };


                        Cmd.Parameters.Add(epaId);
                        Cmd.Parameters.Add(preguntaId);
                        Cmd.Parameters.Add(texto);
                        Cmd.Parameters.Add(validacion);
                        // Loop through the results and add to list

                        // Execute SQL Command
                        SqlDataReader rdr = Cmd.ExecuteReader();

                        string resultado = validacion.SqlValue.ToString();
                        result = -3;
                        if (resultado != null && resultado != "Null")
                        {
                            result = int.Parse(resultado);
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error: " + ex);
                        Console.WriteLine("Error Message: " + ex.Message);
                        throw;
                    }
                }
            }
        }

        public int XIXX_INSERTA_EPA_INICIAL(string conversationId, string agentName, string organizationId, string queueName)
        {
            int result;

            using (var Conn = new SqlConnection(DbConnection.GetStringConnection()))
            {
                //set stored procedure name
                string spName = @"[dbo].[XXUI_INSERTA_ENVIO_ENCUESTA_V2]";

                //define the SqlCommand object
                using (var Cmd = new SqlCommand(spName, Conn))
                {

                    Cmd.CommandType = System.Data.CommandType.StoredProcedure;

                    try
                    {
                        // Open SQL Connection
                        Conn.Open();

                        var ConversationId = new SqlParameter()
                        {
                            ParameterName = "@CONVERSATION_ID",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 50,
                            Value = conversationId
                        };

                        var AgentName = new SqlParameter()
                        {
                            ParameterName = "@AGENTE",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 100,
                            Value = agentName
                        };

                        var OrganizationId = new SqlParameter()
                        {
                            ParameterName = "@ORGANIZATION_ID",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 50,
                            Value = organizationId
                        };

                        var Cola = new SqlParameter()
                        {
                            ParameterName = "@COLA",
                            SqlDbType = System.Data.SqlDbType.VarChar,
                            Direction = System.Data.ParameterDirection.Input,
                            Size = 100,
                            Value = queueName
                        };

                        var Return = new SqlParameter()
                        {
                            ParameterName = "@RETURN",
                            SqlDbType = System.Data.SqlDbType.BigInt,
                            Direction = System.Data.ParameterDirection.Output
                        };


                        Cmd.Parameters.Add(ConversationId);
                        Cmd.Parameters.Add(AgentName);
                        Cmd.Parameters.Add(OrganizationId);
                        Cmd.Parameters.Add(Cola);
                        Cmd.Parameters.Add(Return);

                        // Loop through the results and add to list

                        // Execute SQL Command

                        SqlDataReader rdr = Cmd.ExecuteReader();

                        string resultado = Return.SqlValue.ToString();
                        result = -3;
                        if (resultado != null && resultado != "Null")
                        {
                            result = int.Parse(resultado);
                        }

                        return result;
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine("Error: " + ex);
                        Console.WriteLine("Error Message: " + ex.Message);
                        throw;
                    }
                }
            }

        }
    }
}
